# إعدادات Facebook API
import os
from dotenv import load_dotenv

# تحميل المتغيرات من ملف .env
load_dotenv()

class FacebookConfig:
    """إعدادات Facebook API"""
    
    # معلومات التطبيق - ضع قيمك هنا بعد إنشاء التطبيق
    APP_ID = os.getenv('FACEBOOK_APP_ID', 'YOUR_APP_ID_HERE')
    APP_SECRET = os.getenv('FACEBOOK_APP_SECRET', 'YOUR_APP_SECRET_HERE')
    
    # إعدادات API
    API_VERSION = 'v18.0'
    BASE_URL = f'https://graph.facebook.com/{API_VERSION}'
    
    # الصلاحيات المطلوبة
    PERMISSIONS = [
        'public_profile',
        'pages_show_list',
        'pages_read_engagement',
        'pages_read_user_content'
    ]
    
    # URLs للمصادقة
    REDIRECT_URI = 'http://localhost:8000/auth/callback'
    
    @classmethod
    def get_app_access_token_url(cls):
        """رابط الحصول على App Access Token"""
        return f"{cls.BASE_URL}/oauth/access_token?client_id={cls.APP_ID}&client_secret={cls.APP_SECRET}&grant_type=client_credentials"
    
    @classmethod
    def get_user_auth_url(cls):
        """رابط مصادقة المستخدم"""
        permissions = ','.join(cls.PERMISSIONS)
        return f"https://www.facebook.com/v{cls.API_VERSION.split('v')[1]}/dialog/oauth?client_id={cls.APP_ID}&redirect_uri={cls.REDIRECT_URI}&scope={permissions}"
    
    @classmethod
    def validate_config(cls):
        """التحقق من صحة الإعدادات"""
        if cls.APP_ID == 'YOUR_APP_ID_HERE' or cls.APP_SECRET == 'YOUR_APP_SECRET_HERE':
            raise ValueError("يرجى تحديث APP_ID و APP_SECRET في ملف .env")
        return True

# مثال على الاستخدام
if __name__ == "__main__":
    try:
        FacebookConfig.validate_config()
        print("✅ إعدادات Facebook صحيحة")
        print(f"App ID: {FacebookConfig.APP_ID}")
        print(f"API Version: {FacebookConfig.API_VERSION}")
    except ValueError as e:
        print(f"❌ خطأ في الإعدادات: {e}")
