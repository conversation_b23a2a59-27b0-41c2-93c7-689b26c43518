"""
اختبار الاتصال مع Facebook API
"""

import os
import requests
from dotenv import load_dotenv

# تحميل المتغيرات
load_dotenv()

def test_facebook_connection():
    """اختبار الاتصال مع Facebook API"""
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    app_secret = os.getenv('FACEBOOK_APP_SECRET')
    
    print("🔍 اختبار إعدادات Facebook API...")
    print(f"App ID: {app_id}")
    
    if not app_id or app_id == 'your_app_id_here':
        print("❌ App ID غير محدد في ملف .env")
        return False
        
    if not app_secret or app_secret == 'your_app_secret_here':
        print("❌ App Secret غير محدد في ملف .env")
        return False
    
    # اختبار الحصول على Access Token
    print("\n🔑 محاولة الحصول على Access Token...")
    
    url = "https://graph.facebook.com/v18.0/oauth/access_token"
    params = {
        'client_id': app_id,
        'client_secret': app_secret,
        'grant_type': 'client_credentials'
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        access_token = data.get('access_token')
        
        if access_token:
            print("✅ تم الحصول على Access Token بنجاح!")
            print(f"Token: {access_token[:20]}...")
            
            # اختبار استخدام التوكن
            test_url = f"https://graph.facebook.com/v18.0/me?access_token={access_token}"
            test_response = requests.get(test_url)
            
            if test_response.status_code == 200:
                print("✅ التوكن يعمل بشكل صحيح!")
                return True
            else:
                print(f"⚠️ التوكن لا يعمل: {test_response.status_code}")
                print(test_response.text)
                return False
        else:
            print("❌ فشل في الحصول على Access Token")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = test_facebook_connection()
    
    if success:
        print("\n🎉 الإعداد مكتمل! يمكنك الآن استخدام facebook_news_collector.py")
    else:
        print("\n❌ يرجى التحقق من الإعدادات وإعادة المحاولة")
        print("\nتأكد من:")
        print("1. وضع App Secret الصحيح في ملف .env")
        print("2. إضافة Facebook Login في لوحة التحكم")
        print("3. التأكد من أن التطبيق نشط")
