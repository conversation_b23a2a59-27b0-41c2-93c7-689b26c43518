"""
iNews - جامع الأخبار من Facebook
نظام متكامل لجمع الأخبار من صفحات الفيسبوك
"""

import requests
import json
import pandas as pd
from datetime import datetime
import time

class iNewsCollector:
    """جامع الأخبار من Facebook"""
    
    def __init__(self):
        self.app_id = "1905779223574690"
        self.app_secret = "1bd1c17a6de438016b9755fcd6e18171"
        self.access_token = None
        self.api_version = "v18.0"
        self.base_url = f"https://graph.facebook.com/{self.api_version}"
        
    def get_access_token(self):
        """الحصول على Access Token"""
        url = f"{self.base_url}/oauth/access_token"
        params = {
            'client_id': self.app_id,
            'client_secret': self.app_secret,
            'grant_type': 'client_credentials'
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get('access_token')
                print(f"✅ تم الحصول على Access Token: {self.access_token[:30]}...")
                return True
            else:
                print(f"❌ فشل في الحصول على Access Token: {response.text}")
                return False
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False
    
    def search_news_pages(self, query="news", limit=20):
        """البحث عن صفحات الأخبار"""
        if not self.access_token:
            if not self.get_access_token():
                return []
        
        url = f"{self.base_url}/search"
        params = {
            'q': query,
            'type': 'page',
            'fields': 'id,name,category,fan_count,about,website',
            'limit': limit,
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                pages = data.get('data', [])
                print(f"🔍 تم العثور على {len(pages)} صفحة")
                return pages
            else:
                print(f"❌ فشل البحث: {response.text}")
                return []
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            return []
    
    def get_page_info(self, page_id):
        """الحصول على معلومات صفحة"""
        if not self.access_token:
            if not self.get_access_token():
                return None
        
        url = f"{self.base_url}/{page_id}"
        params = {
            'fields': 'id,name,about,category,fan_count,website,posts.limit(10){id,message,story,created_time,type,link,reactions.summary(true),comments.summary(true),shares}',
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ فشل في جلب معلومات الصفحة {page_id}: {response.text}")
                return None
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return None
    
    def get_public_posts(self, page_id, limit=10):
        """جلب المنشورات العامة من صفحة"""
        if not self.access_token:
            if not self.get_access_token():
                return []
        
        url = f"{self.base_url}/{page_id}/posts"
        params = {
            'fields': 'id,message,story,created_time,type,link,reactions.summary(true),comments.summary(true),shares',
            'limit': limit,
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return data.get('data', [])
            else:
                print(f"❌ فشل في جلب منشورات الصفحة {page_id}: {response.text}")
                return []
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return []
    
    def collect_news_from_pages(self, page_ids, posts_per_page=5):
        """جمع الأخبار من قائمة صفحات"""
        all_news = []
        
        for i, page_id in enumerate(page_ids, 1):
            print(f"\n📰 [{i}/{len(page_ids)}] جلب الأخبار من الصفحة: {page_id}")
            
            # معلومات الصفحة
            page_info = self.get_page_info(page_id)
            if not page_info:
                continue
            
            page_name = page_info.get('name', 'Unknown')
            print(f"📄 اسم الصفحة: {page_name}")
            
            # المنشورات
            posts = self.get_public_posts(page_id, posts_per_page)
            print(f"📝 تم جلب {len(posts)} منشور")
            
            for post in posts:
                news_item = {
                    'page_name': page_name,
                    'page_id': page_id,
                    'post_id': post.get('id'),
                    'message': post.get('message', ''),
                    'story': post.get('story', ''),
                    'created_time': post.get('created_time'),
                    'type': post.get('type'),
                    'link': post.get('link'),
                    'reactions_count': post.get('reactions', {}).get('summary', {}).get('total_count', 0),
                    'comments_count': post.get('comments', {}).get('summary', {}).get('total_count', 0),
                    'shares_count': post.get('shares', {}).get('count', 0) if post.get('shares') else 0,
                    'collected_at': datetime.now().isoformat()
                }
                all_news.append(news_item)
            
            # توقف قصير لتجنب Rate Limiting
            time.sleep(1)
        
        return all_news
    
    def save_to_csv(self, news_data, filename=None):
        """حفظ البيانات في ملف CSV"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"facebook_news_{timestamp}.csv"
        
        df = pd.DataFrame(news_data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"💾 تم حفظ {len(news_data)} خبر في ملف: {filename}")
        return filename
    
    def save_to_json(self, news_data, filename=None):
        """حفظ البيانات في ملف JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"facebook_news_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(news_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ {len(news_data)} خبر في ملف: {filename}")
        return filename

# مثال على الاستخدام
if __name__ == "__main__":
    print("🚀 بدء تشغيل iNews Collector")
    
    # إنشاء جامع الأخبار
    collector = iNewsCollector()
    
    # اختبار الاتصال
    if collector.get_access_token():
        print("\n🔍 البحث عن صفحات الأخبار...")
        
        # البحث عن صفحات أخبار
        news_pages = collector.search_news_pages("news", 10)
        
        if news_pages:
            print(f"\n📋 تم العثور على {len(news_pages)} صفحة أخبار:")
            for page in news_pages[:5]:  # عرض أول 5 صفحات
                print(f"  📄 {page.get('name')} (ID: {page.get('id')})")
            
            # جمع الأخبار من أول 3 صفحات
            page_ids = [page['id'] for page in news_pages[:3]]
            print(f"\n📰 جمع الأخبار من {len(page_ids)} صفحات...")
            
            news_data = collector.collect_news_from_pages(page_ids, 3)
            
            if news_data:
                print(f"\n✅ تم جمع {len(news_data)} خبر بنجاح!")
                
                # حفظ البيانات
                csv_file = collector.save_to_csv(news_data)
                json_file = collector.save_to_json(news_data)
                
                print(f"\n📁 الملفات المحفوظة:")
                print(f"  📊 CSV: {csv_file}")
                print(f"  📋 JSON: {json_file}")
            else:
                print("❌ لم يتم جمع أي أخبار")
        else:
            print("❌ لم يتم العثور على صفحات أخبار")
    else:
        print("❌ فشل في الاتصال بـ Facebook API")
