import requests

# القيم المباشرة
app_id = "1905779223574690"
app_secret = "1bd1c17a6de438016b9755fcd6e18171"

print("🔍 اختبار Facebook API مباشرة")
print(f"App ID: {app_id}")
print(f"App Secret: {app_secret[:10]}...")

print("\n🔑 محاولة الحصول على Access Token...")

url = "https://graph.facebook.com/v18.0/oauth/access_token"
params = {
    'client_id': app_id,
    'client_secret': app_secret,
    'grant_type': 'client_credentials'
}

try:
    response = requests.get(url, params=params)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        access_token = data.get('access_token')
        if access_token:
            print(f"✅ نجح! Access Token: {access_token[:30]}...")
            
            # اختبار استخدام التوكن
            print("\n🧪 اختبار استخدام التوكن...")
            test_url = f"https://graph.facebook.com/v18.0/me?access_token={access_token}"
            test_response = requests.get(test_url)
            print(f"Test Status: {test_response.status_code}")
            print(f"Test Response: {test_response.text}")
            
            # اختبار البحث عن صفحات
            print("\n🔍 اختبار البحث عن صفحات...")
            search_url = f"https://graph.facebook.com/v18.0/search?q=news&type=page&access_token={access_token}"
            search_response = requests.get(search_url)
            print(f"Search Status: {search_response.status_code}")
            print(f"Search Response: {search_response.text[:200]}...")
            
        else:
            print("❌ لم يتم العثور على access_token في الاستجابة")
    else:
        print(f"❌ فشل الطلب: {response.status_code}")
        
except Exception as e:
    print(f"❌ خطأ: {e}")
