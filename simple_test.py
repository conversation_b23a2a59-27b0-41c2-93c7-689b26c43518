import os
import requests

# قراءة المتغيرات مباشرة من ملف .env
def load_env():
    env_vars = {}
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    except Exception as e:
        print(f"خطأ في قراءة ملف .env: {e}")
    return env_vars

# تحميل المتغيرات
env_vars = load_env()
app_id = env_vars.get('FACEBOOK_APP_ID')
app_secret = env_vars.get('FACEBOOK_APP_SECRET')

print(f"App ID: {app_id}")
print(f"App Secret: {app_secret[:10]}..." if app_secret else "App Secret: غير موجود")

if app_id and app_secret:
    print("\n🔑 محاولة الحصول على Access Token...")
    
    url = "https://graph.facebook.com/v18.0/oauth/access_token"
    params = {
        'client_id': app_id,
        'client_secret': app_secret,
        'grant_type': 'client_credentials'
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access_token')
            if access_token:
                print(f"✅ نجح! Access Token: {access_token[:20]}...")
            else:
                print("❌ لم يتم العثور على access_token في الاستجابة")
        else:
            print(f"❌ فشل الطلب: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
else:
    print("❌ App ID أو App Secret مفقود")
