"""
iNews - Facebook News Collector
جامع الأخبار من صفحات الفيسبوك
"""

import os
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import pandas as pd

# تحميل المتغيرات من ملف .env
load_dotenv()

class FacebookNewsCollector:
    """كلاس لجمع الأخبار من صفحات الفيسبوك"""
    
    def __init__(self):
        self.app_id = os.getenv('FACEBOOK_APP_ID')
        self.app_secret = os.getenv('FACEBOOK_APP_SECRET')
        self.api_version = 'v18.0'
        self.base_url = f'https://graph.facebook.com/{self.api_version}'
        self.access_token = None
        
    def get_app_access_token(self):
        """الحصول على App Access Token"""
        url = f"{self.base_url}/oauth/access_token"
        params = {
            'client_id': self.app_id,
            'client_secret': self.app_secret,
            'grant_type': 'client_credentials'
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            self.access_token = data['access_token']
            print("✅ تم الحصول على Access Token بنجاح")
            return self.access_token
        except Exception as e:
            print(f"❌ خطأ في الحصول على Access Token: {e}")
            return None
    
    def get_page_info(self, page_id):
        """الحصول على معلومات صفحة"""
        if not self.access_token:
            self.get_app_access_token()
            
        url = f"{self.base_url}/{page_id}"
        params = {
            'fields': 'id,name,about,category,fan_count,website',
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ خطأ في جلب معلومات الصفحة: {e}")
            return None
    
    def get_page_posts(self, page_id, limit=10):
        """جلب منشورات الصفحة"""
        if not self.access_token:
            self.get_app_access_token()
            
        url = f"{self.base_url}/{page_id}/posts"
        params = {
            'fields': 'id,message,story,created_time,type,link,reactions.summary(true),comments.summary(true),shares',
            'limit': limit,
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ خطأ في جلب المنشورات: {e}")
            return None
    
    def search_pages(self, query, limit=10):
        """البحث عن صفحات"""
        if not self.access_token:
            self.get_app_access_token()
            
        url = f"{self.base_url}/search"
        params = {
            'q': query,
            'type': 'page',
            'fields': 'id,name,category,fan_count',
            'limit': limit,
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            return None
    
    def collect_news_from_pages(self, page_ids, posts_per_page=5):
        """جمع الأخبار من قائمة صفحات"""
        all_news = []
        
        for page_id in page_ids:
            print(f"📰 جلب الأخبار من الصفحة: {page_id}")
            
            # معلومات الصفحة
            page_info = self.get_page_info(page_id)
            if not page_info:
                continue
                
            # منشورات الصفحة
            posts_data = self.get_page_posts(page_id, posts_per_page)
            if not posts_data or 'data' not in posts_data:
                continue
                
            for post in posts_data['data']:
                news_item = {
                    'page_name': page_info.get('name', 'Unknown'),
                    'page_id': page_id,
                    'post_id': post.get('id'),
                    'message': post.get('message', ''),
                    'story': post.get('story', ''),
                    'created_time': post.get('created_time'),
                    'type': post.get('type'),
                    'link': post.get('link'),
                    'reactions_count': post.get('reactions', {}).get('summary', {}).get('total_count', 0),
                    'comments_count': post.get('comments', {}).get('summary', {}).get('total_count', 0),
                    'shares_count': post.get('shares', {}).get('count', 0) if post.get('shares') else 0
                }
                all_news.append(news_item)
        
        return all_news
    
    def save_to_csv(self, news_data, filename=None):
        """حفظ البيانات في ملف CSV"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"facebook_news_{timestamp}.csv"
            
        df = pd.DataFrame(news_data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"💾 تم حفظ {len(news_data)} خبر في ملف: {filename}")
        return filename

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء جامع الأخبار
    collector = FacebookNewsCollector()
    
    # اختبار الاتصال
    token = collector.get_app_access_token()
    if token:
        print(f"🔑 Access Token: {token[:20]}...")
        
        # مثال: البحث عن صفحات أخبار
        print("\n🔍 البحث عن صفحات الأخبار...")
        search_results = collector.search_pages("news", 5)
        if search_results and 'data' in search_results:
            for page in search_results['data']:
                print(f"📄 {page['name']} (ID: {page['id']})")
    else:
        print("❌ فشل في الحصول على Access Token")
        print("تأكد من وضع APP_ID و APP_SECRET في ملف .env")
