"""
iNews Web Interface
واجهة ويب لإدارة جمع الأخبار من Facebook
"""

from flask import Flask, render_template, request, jsonify, send_file
import json
import os
from datetime import datetime
import re
from inews_collector import iNewsCollector

app = Flask(__name__)

class PageManager:
    """إدارة صفحات الأخبار"""
    
    def __init__(self):
        self.pages_file = "pages.json"
        self.load_pages()
    
    def load_pages(self):
        """تحميل قائمة الصفحات"""
        if os.path.exists(self.pages_file):
            with open(self.pages_file, 'r', encoding='utf-8') as f:
                self.pages = json.load(f)
        else:
            self.pages = []
    
    def save_pages(self):
        """حفظ قائمة الصفحات"""
        with open(self.pages_file, 'w', encoding='utf-8') as f:
            json.dump(self.pages, f, ensure_ascii=False, indent=2)
    
    def extract_page_id(self, url):
        """استخراج Page ID من رابط Facebook"""
        # أنماط مختلفة لروابط Facebook
        patterns = [
            r'facebook\.com/([^/?]+)',
            r'facebook\.com/pages/[^/]+/(\d+)',
            r'facebook\.com/profile\.php\?id=(\d+)',
            r'^(\d+)$'  # إذا كان Page ID مباشرة
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def add_page(self, url, name=None, category=None):
        """إضافة صفحة جديدة"""
        page_id = self.extract_page_id(url)
        if not page_id:
            return False, "رابط غير صحيح"
        
        # التحقق من عدم وجود الصفحة مسبقاً
        for page in self.pages:
            if page['id'] == page_id:
                return False, "الصفحة موجودة مسبقاً"
        
        page_data = {
            'id': page_id,
            'url': url,
            'name': name or f"صفحة {page_id}",
            'category': category or "عام",
            'added_at': datetime.now().isoformat(),
            'active': True
        }
        
        self.pages.append(page_data)
        self.save_pages()
        return True, "تم إضافة الصفحة بنجاح"
    
    def remove_page(self, page_id):
        """حذف صفحة"""
        self.pages = [p for p in self.pages if p['id'] != page_id]
        self.save_pages()
        return True, "تم حذف الصفحة"
    
    def toggle_page(self, page_id):
        """تفعيل/إلغاء تفعيل صفحة"""
        for page in self.pages:
            if page['id'] == page_id:
                page['active'] = not page['active']
                self.save_pages()
                return True, f"تم {'تفعيل' if page['active'] else 'إلغاء تفعيل'} الصفحة"
        return False, "الصفحة غير موجودة"
    
    def get_active_pages(self):
        """الحصول على الصفحات النشطة"""
        return [p for p in self.pages if p['active']]

# إنشاء مدير الصفحات
page_manager = PageManager()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', pages=page_manager.pages)

@app.route('/add_page', methods=['POST'])
def add_page():
    """إضافة صفحة جديدة"""
    data = request.get_json()
    url = data.get('url', '').strip()
    name = data.get('name', '').strip()
    category = data.get('category', '').strip()
    
    if not url:
        return jsonify({'success': False, 'message': 'الرابط مطلوب'})
    
    success, message = page_manager.add_page(url, name, category)
    return jsonify({'success': success, 'message': message})

@app.route('/remove_page', methods=['POST'])
def remove_page():
    """حذف صفحة"""
    data = request.get_json()
    page_id = data.get('page_id')
    
    success, message = page_manager.remove_page(page_id)
    return jsonify({'success': success, 'message': message})

@app.route('/toggle_page', methods=['POST'])
def toggle_page():
    """تفعيل/إلغاء تفعيل صفحة"""
    data = request.get_json()
    page_id = data.get('page_id')
    
    success, message = page_manager.toggle_page(page_id)
    return jsonify({'success': success, 'message': message})

@app.route('/collect_news', methods=['POST'])
def collect_news():
    """جمع الأخبار من الصفحات النشطة"""
    try:
        data = request.get_json()
        posts_per_page = data.get('posts_per_page', 5)
        
        # الحصول على الصفحات النشطة
        active_pages = page_manager.get_active_pages()
        if not active_pages:
            return jsonify({'success': False, 'message': 'لا توجد صفحات نشطة'})
        
        # إنشاء جامع الأخبار
        collector = iNewsCollector()
        
        # جمع الأخبار
        page_ids = [page['id'] for page in active_pages]
        news_data = collector.collect_news_from_pages(page_ids, posts_per_page)
        
        if news_data:
            # حفظ البيانات
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_file = f"news_{timestamp}.csv"
            json_file = f"news_{timestamp}.json"
            
            collector.save_to_csv(news_data, csv_file)
            collector.save_to_json(news_data, json_file)
            
            return jsonify({
                'success': True,
                'message': f'تم جمع {len(news_data)} خبر بنجاح',
                'news_count': len(news_data),
                'csv_file': csv_file,
                'json_file': json_file
            })
        else:
            return jsonify({'success': False, 'message': 'لم يتم جمع أي أخبار'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

@app.route('/download/<filename>')
def download_file(filename):
    """تحميل ملف"""
    try:
        return send_file(filename, as_attachment=True)
    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل الملف: {str(e)}'})

@app.route('/test_connection')
def test_connection():
    """اختبار الاتصال مع Facebook API"""
    try:
        collector = iNewsCollector()
        success = collector.get_access_token()
        
        if success:
            return jsonify({'success': True, 'message': 'الاتصال ناجح'})
        else:
            return jsonify({'success': False, 'message': 'فشل الاتصال'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

if __name__ == '__main__':
    print("🚀 بدء تشغيل iNews Web Interface")
    print("📱 الواجهة متاحة على: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
