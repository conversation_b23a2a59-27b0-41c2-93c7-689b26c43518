import requests

# معلومات التطبيق
app_id = "1905779223574690"

print("🔍 تشخيص مشكلة Facebook API")
print(f"App ID: {app_id}")

# اختبار 1: التحقق من وجود التطبيق
print("\n1️⃣ اختبار وجود التطبيق...")
test_url = f"https://graph.facebook.com/v18.0/{app_id}"
try:
    response = requests.get(test_url)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"خطأ: {e}")

# اختبار 2: محاولة الحصول على معلومات عامة
print("\n2️⃣ اختبار الوصول للمعلومات العامة...")
public_url = f"https://graph.facebook.com/v18.0/{app_id}?fields=name,category"
try:
    response = requests.get(public_url)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"خطأ: {e}")

# اختبار 3: اختبار مع App Secret
print("\n3️⃣ اختبار مع App Secret...")
app_secret = input("أدخل App Secret الصحيح: ").strip()

if app_secret:
    token_url = "https://graph.facebook.com/v18.0/oauth/access_token"
    params = {
        'client_id': app_id,
        'client_secret': app_secret,
        'grant_type': 'client_credentials'
    }
    
    try:
        response = requests.get(token_url, params=params)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access_token')
            if access_token:
                print(f"✅ نجح! Access Token: {access_token[:20]}...")
                
                # حفظ App Secret الصحيح
                with open('.env', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # تحديث App Secret
                content = content.replace('FACEBOOK_APP_SECRET=1bd1c17a6de438016b9755fcd6e18171', 
                                        f'FACEBOOK_APP_SECRET={app_secret}')
                
                with open('.env', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ تم حفظ App Secret الصحيح في ملف .env")
            else:
                print("❌ لم يتم العثور على access_token")
        else:
            print("❌ فشل في الحصول على Access Token")
            
    except Exception as e:
        print(f"خطأ: {e}")

print("\n📋 خطوات إضافية مطلوبة:")
print("1. تأكد من إضافة Facebook Login في لوحة التحكم")
print("2. تأكد من أن التطبيق في وضع Development")
print("3. تحقق من App Secret في Settings > Basic")
