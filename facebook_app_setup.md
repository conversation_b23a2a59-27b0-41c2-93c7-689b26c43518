# إعداد تطبيق Facebook للأخبار

## معلومات التطبيق
```
App Name: iNews Aggregator
App ID: [ضع هنا App ID بعد الإنشاء]
App Secret: [ضع هنا App Secret - احتفظ به سرياً]
```

## خطوات الإعداد المطلوبة:

### 1. اختيار حالة الاستخدام:
- ✅ Content management
- ✅ "Authenticate and request data from users"

### 2. المنتجات المطلوبة:
- ✅ Facebook Login
- ✅ Pages API
- ✅ Instagram Basic Display (اختياري)

### 3. الصلاحيات المطلوبة:
```
Basic Permissions:
- public_profile
- email

Pages Permissions:
- pages_show_list
- pages_read_engagement
- pages_read_user_content
```

### 4. إعدادات Facebook Login:
```
Valid OAuth Redirect URIs:
- http://localhost:3000/auth/callback
- https://yourdomain.com/auth/callback

Valid Deauthorize Callback URL:
- https://yourdomain.com/auth/deauthorize
```

### 5. معلومات الخصوصية (مطلوبة للمراجعة):
```
Privacy Policy URL: [رابط سياسة الخصوصية]
Terms of Service URL: [رابط شروط الخدمة]
App Icon: [صورة 1024x1024 بكسل]
```

## الخطوات التالية بعد الإنشاء:

### 1. اختبار التطبيق:
- استخدم Graph API Explorer
- جرب الحصول على Access Token
- اختبر جلب بيانات صفحة عامة

### 2. تطوير التطبيق:
- إنشاء كود Python للاتصال
- تنفيذ نظام جلب الأخبار
- إضافة قاعدة بيانات لحفظ البيانات

### 3. طلب المراجعة (إذا لزم الأمر):
- تحضير فيديو توضيحي
- كتابة وصف مفصل للاستخدام
- إرسال طلب App Review

## ملاحظات مهمة:
- احتفظ بـ App Secret في مكان آمن
- لا تشارك Access Tokens
- استخدم HTTPS في الإنتاج
- اقرأ سياسات Facebook بعناية
